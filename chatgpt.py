#!/usr/bin/env python3
"""
ChatGPT调用脚本
使用验证过的可用API密钥调用OpenAI GPT模型
"""

import openai
import sys
import json
from typing import List, Dict, Optional

# 唯一可用的API密钥
API_KEY = "***************************************************"

class ChatGPT:
    def __init__(self, api_key: str = API_KEY):
        """初始化ChatGPT客户端"""
        self.client = openai.OpenAI(api_key=api_key)
        self.conversation_history = []
    
    def chat(self, message: str, 
             model: str = "gpt-3.5-turbo",
             temperature: float = 0.7,
             max_tokens: Optional[int] = None,
             system_message: str = None) -> str:
        """
        发送消息给ChatGPT并获取回复
        
        Args:
            message: 用户消息
            model: 使用的模型 (gpt-3.5-turbo, gpt-4等)
            temperature: 温度参数 (0-2)
            max_tokens: 最大token数
            system_message: 系统消息
            
        Returns:
            ChatGPT的回复
        """
        try:
            messages = []
            
            # 添加系统消息
            if system_message:
                messages.append({"role": "system", "content": system_message})
            
            # 添加对话历史
            messages.extend(self.conversation_history)
            
            # 添加当前用户消息
            messages.append({"role": "user", "content": message})
            
            # 调用API
            response = self.client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens
            )
            
            reply = response.choices[0].message.content
            
            # 更新对话历史
            self.conversation_history.append({"role": "user", "content": message})
            self.conversation_history.append({"role": "assistant", "content": reply})
            
            # 限制历史长度，避免token过多
            if len(self.conversation_history) > 10:
                self.conversation_history = self.conversation_history[-10:]
            
            return reply
            
        except Exception as e:
            return f"❌ 调用失败: {str(e)}"
    
    def simple_chat(self, message: str) -> str:
        """简单对话，不保存历史"""
        try:
            response = self.client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "你是一个友好的AI助手，请用中文回答问题。"},
                    {"role": "user", "content": message}
                ],
                temperature=0.7,
                max_tokens=1000
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            return f"❌ 调用失败: {str(e)}"
    
    def clear_history(self):
        """清除对话历史"""
        self.conversation_history = []
        print("✅ 对话历史已清除")
    
    def save_conversation(self, filename: str = "conversation.json"):
        """保存对话历史到文件"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.conversation_history, f, indent=2, ensure_ascii=False)
            print(f"✅ 对话历史已保存到: {filename}")
        except Exception as e:
            print(f"❌ 保存失败: {e}")
    
    def load_conversation(self, filename: str = "conversation.json"):
        """从文件加载对话历史"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                self.conversation_history = json.load(f)
            print(f"✅ 对话历史已从 {filename} 加载")
        except FileNotFoundError:
            print(f"❌ 文件 {filename} 不存在")
        except Exception as e:
            print(f"❌ 加载失败: {e}")

def interactive_mode():
    """交互模式"""
    print("🤖 ChatGPT 交互模式")
    print("=" * 50)
    print("输入 'quit' 或 'exit' 退出")
    print("输入 'clear' 清除对话历史")
    print("输入 'save' 保存对话历史")
    print("输入 'load' 加载对话历史")
    print("=" * 50)
    
    gpt = ChatGPT()
    
    while True:
        try:
            user_input = input("\n👤 你: ").strip()
            
            if user_input.lower() in ['quit', 'exit', '退出']:
                print("👋 再见！")
                break
            elif user_input.lower() == 'clear':
                gpt.clear_history()
                continue
            elif user_input.lower() == 'save':
                gpt.save_conversation()
                continue
            elif user_input.lower() == 'load':
                gpt.load_conversation()
                continue
            elif not user_input:
                continue
            
            print("🤖 ChatGPT: ", end="", flush=True)
            response = gpt.chat(user_input)
            print(response)
            
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"\n❌ 错误: {e}")

def single_question_mode(question: str):
    """单次问答模式"""
    gpt = ChatGPT()
    response = gpt.simple_chat(question)
    print(f"🤖 ChatGPT: {response}")

def main():
    """主函数"""
    if len(sys.argv) == 1:
        # 没有参数，启动交互模式
        interactive_mode()
    elif len(sys.argv) == 2:
        # 有一个参数，单次问答
        question = sys.argv[1]
        single_question_mode(question)
    else:
        print("使用方法:")
        print("  python chatgpt.py                    # 交互模式")
        print("  python chatgpt.py '你的问题'          # 单次问答")

if __name__ == "__main__":
    # 检查是否安装了openai库
    try:
        import openai
    except ImportError:
        print("❌ 缺少依赖包 openai")
        print("请运行: pip install openai")
        sys.exit(1)
    
    main()
