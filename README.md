# ChatGPT Python 调用脚本

这是一个简洁的ChatGPT调用脚本，使用经过验证的可用API密钥。

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 交互模式（推荐）

```bash
python chatgpt.py
```

在交互模式下，你可以：
- 与ChatGPT进行连续对话
- 输入 `clear` 清除对话历史
- 输入 `save` 保存对话历史
- 输入 `load` 加载对话历史
- 输入 `quit` 或 `exit` 退出

### 2. 单次问答模式

```bash
python chatgpt.py "你的问题"
```

例如：
```bash
python chatgpt.py "请解释什么是人工智能"
```

## 功能特点

- ✅ 使用经过验证的可用API密钥
- 🔄 支持连续对话，保持上下文
- 💾 可以保存和加载对话历史
- 🎯 简洁易用的命令行界面
- ⚡ 支持单次问答和交互模式

## 注意事项

- 脚本内置了唯一可用的API密钥
- 对话历史会自动限制长度以避免token过多
- 请合理使用以避免超出API配额
- 如遇到错误，请检查网络连接和API状态

## 文件说明

- `chatgpt.py` - 主要的ChatGPT调用脚本
- `requirements.txt` - Python依赖包列表
- `conversation.json` - 对话历史保存文件（使用后自动生成）
